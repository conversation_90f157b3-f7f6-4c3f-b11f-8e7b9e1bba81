import argparse
import os

# from memory_manager import <PERSON><PERSON>ana<PERSON>
from memzero.manager import Mem<PERSON><PERSON>anager
from graphiti.manager import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ger
from utils import TECHNIQUES


def main():
    parser = argparse.ArgumentParser(description="Run memory experiments")
    parser.add_argument("--input_dataset", type=str,
                        default="dataset/locomo10.json", help="Input path for dataset")
    parser.add_argument("--technique_type", choices=TECHNIQUES,
                        default="mem0", help="Memory technique to use")
    parser.add_argument("--chunk_size", type=int,
                        default=1000, help="Chunk size for processing")
    parser.add_argument("--output_folder", type=str,
                        default="results/", help="Output path for results")
    parser.add_argument("--is_graph", action="store_true",
                        default=False, help="Whether to use graph-based search")
    parser.add_argument("--num_chunks", type=int, default=1,
                        help="Number of chunks to process")

    args = parser.parse_args()

    print(
        f"Running experiments with technique: {args.technique_type}, chunk size: {args.chunk_size}")

    if args.technique_type == "mem0":
        output_file_path = os.path.join(
            args.output_folder,
            f"mem0_results_graph_{args.is_graph}.json",
        )
        mem0_manager = Mem0Manager(data_path=args.input_dataset, is_graph=args.is_graph,
                                   output_path=output_file_path)
        mem0_manager.process_all_conversations()
        mem0_manager.process_data_file(args.input_dataset)
    # elif args.technique_type == "rag":
    #     output_file_path = os.path.join(args.output_folder, f"rag_results_{args.chunk_size}_k{args.num_chunks}.json")
    #     rag_manager = RAGManager(data_path="dataset/locomo10_rag.json", chunk_size=args.chunk_size, k=args.num_chunks)
    #     rag_manager.process_all_conversations(output_file_path)
    elif args.technique_type == "grphiti":
        output_file_path = os.path.join(
            args.output_folder,
            f"graphiti_results_graph_{args.is_graph}.json",
        )
        graphiti_manager = GraphitiManager(data_path=args.input_dataset, is_graph=args.is_graph,
                                          output_path=output_file_path)
        try:
            graphiti_manager.process_all_conversations()
            graphiti_manager.process_data_file(args.input_dataset)
        finally:
            # Ensure connections are properly closed
            graphiti_manager.close_all_connections()
    # elif args.technique_type == "memvid":
    #     output_file_path = os.path.join(args.output_folder, "langmem_results.json")
    #     langmem_manager = LangMemManager(dataset_path="dataset/locomo10_rag.json")
    #     langmem_manager.process_all_conversations(output_file_path)
    # elif args.technique_type == "memoryos":
    #     output_file_path = os.path.join(args.output_folder, "openai_results.json")
    #     openai_manager = OpenAIPredict()
    #     openai_manager.process_data_file("dataset/locomo10.json", output_file_path)
    # else:
    #     raise ValueError(f"Invalid technique type: {args.technique_type}")


if __name__ == "__main__":
    main()
