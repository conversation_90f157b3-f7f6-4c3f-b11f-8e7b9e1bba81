import time
from memory_manager import MemoryManager
from mem0.memory.main import Memory
from config.config import config


class Mem0<PERSON>anager(MemoryManager):
    
    def _init_memory_client(self):
        return Memory.from_config(config)

    def _add_memory(self, message, user_id, metadata):
        return self.memory.add(message, user_id=user_id, metadata=metadata)

    def _search_memory(self, user_id, query):
        memories = self.memory.search(
            query,
            user_id=user_id,
        )
        if not self.is_graph:
            semantic_memories = [
                {
                    "memory": memory["memory"],
                    "timestamp": memory["metadata"]["timestamp"],
                    "score": round(memory["score"], 2),
                }
                for memory in memories["results"]
            ]
            graph_memories = None
        else:
            semantic_memories = [
                {
                    "memory": memory["memory"],
                    "timestamp": memory["metadata"]["timestamp"],
                    "score": round(memory["score"], 2),
                }
                for memory in memories["results"]
            ]
            graph_memories = [
                {"source": relation["source"], "relationship": relation["relationship"],
                    "target": relation["destination"]}
                for relation in memories["relations"]
            ]
        return semantic_memories, graph_memories
    
    def _delete_memory(self, user_id):
        self.memory.delete_all(user_id=user_id)
