#!/usr/bin/env python3
"""
Demonstration of Graphiti _delete_memory method usage
"""

from datetime import datetime, timezone
from graphiti.manager import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>


def demo_delete_memory():
    """Demonstrate how to use the _delete_memory method"""
    print("=== Graphiti _delete_memory Method Demo ===\n")
    
    # Initialize the manager
    manager = GraphitiManager(is_graph=True)
    
    try:
        # Demo users
        user1 = "alice_demo"
        user2 = "bob_demo"
        
        print("1. Adding memories for multiple users...")
        
        # Add memories for user1
        alice_memories = [
            "<PERSON> loves hiking in the mountains.",
            "<PERSON> works as a software engineer.",
            "<PERSON> has a cat named <PERSON><PERSON><PERSON>."
        ]
        
        for memory in alice_memories:
            metadata = {"timestamp": datetime.now(timezone.utc).isoformat()}
            result = manager._add_memory(memory, user1, metadata)
            print(f"   Added for {user1}: {memory[:30]}...")
        
        # Add memories for user2
        bob_memories = [
            "<PERSON> enjoys cooking Italian food.",
            "<PERSON> is learning to play guitar.",
            "<PERSON> lives in San Francisco."
        ]
        
        for memory in bob_memories:
            metadata = {"timestamp": datetime.now(timezone.utc).isoformat()}
            result = manager._add_memory(memory, user2, metadata)
            print(f"   Added for {user2}: {memory[:30]}...")
        
        print(f"\n2. Searching memories for both users...")
        
        # Search Alice's memories
        alice_results, _ = manager._search_memory(user1, "Alice")
        print(f"   {user1} has {len(alice_results)} memories")
        
        # Search Bob's memories
        bob_results, _ = manager._search_memory(user2, "Bob")
        print(f"   {user2} has {len(bob_results)} memories")
        
        print(f"\n3. Deleting memories for {user1} only...")
        
        # Delete only Alice's memories
        delete_result = manager._delete_memory(user1)
        print(f"   Delete result: {delete_result}")
        
        print(f"\n4. Verifying selective deletion...")
        
        # Check Alice's memories (should be empty)
        alice_results_after, _ = manager._search_memory(user1, "Alice")
        print(f"   {user1} now has {len(alice_results_after)} memories (should be 0)")
        
        # Check Bob's memories (should still exist)
        bob_results_after, _ = manager._search_memory(user2, "Bob")
        print(f"   {user2} still has {len(bob_results_after)} memories (should be {len(bob_results)})")
        
        print(f"\n5. Cleaning up remaining memories...")
        
        # Delete Bob's memories too
        delete_result2 = manager._delete_memory(user2)
        print(f"   Delete result for {user2}: {delete_result2}")
        
        # Final verification
        bob_final, _ = manager._search_memory(user2, "Bob")
        print(f"   {user2} now has {len(bob_final)} memories (should be 0)")
        
        print(f"\n✅ Demo completed successfully!")
        print(f"✅ User isolation verified - deleting one user's memories doesn't affect others")
        
    except Exception as e:
        print(f"❌ Demo failed with error: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # Clean up connections
        print(f"\n6. Cleaning up connections...")
        manager.close_all_connections()
        print("✅ Demo cleanup completed")


if __name__ == "__main__":
    demo_delete_memory()
