#!/usr/bin/env python3
"""
Demonstration of the new Graphiti search + batch delete implementation
This shows how we use Graphiti's official APIs for discovery and Neo4j batch deletion for efficiency
"""

from datetime import datetime, timezone
from graphiti.manager import GraphitiManager


def demo_search_based_delete():
    """Demonstrate the new search-based deletion approach"""
    print("=== Graphiti Search + Batch Delete Demo ===\n")
    
    # Initialize the manager
    manager = GraphitiManager(is_graph=True)
    
    try:
        # Demo user
        user_id = "demo_search_user"
        
        print("1. Adding various types of memories...")
        
        # Add different types of memories to test comprehensive discovery
        memories = [
            "<PERSON> is a software engineer who loves Python programming.",
            "<PERSON> works at TechCorp and leads the AI team.",
            "<PERSON> enjoys hiking on weekends and has climbed Mount Whitney.",
            "<PERSON> has a pet cat named <PERSON><PERSON><PERSON> who is very playful.",
            "<PERSON> graduated from Stanford with a Computer Science degree.",
            "<PERSON> is working on a machine learning project about natural language processing."
        ]
        
        for i, memory in enumerate(memories):
            metadata = {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "memory_type": f"type_{i % 3}",  # Different types to create variety
                "source": "demo"
            }
            result = manager._add_memory(memory, user_id, metadata)
            print(f"   Added memory {i+1}: {memory[:50]}...")
        
        print(f"\n2. Searching to verify memories were added...")
        
        # Search for Alice-related memories
        alice_results, _ = manager._search_memory(user_id, "Alice")
        print(f"   Found {len(alice_results)} Alice-related memories")
        
        # Search for work-related memories
        work_results, _ = manager._search_memory(user_id, "work TechCorp")
        print(f"   Found {len(work_results)} work-related memories")
        
        # Search for hobby-related memories
        hobby_results, _ = manager._search_memory(user_id, "hiking cat")
        print(f"   Found {len(hobby_results)} hobby-related memories")
        
        print(f"\n3. Demonstrating the new search-based deletion...")
        print("   This approach:")
        print("   - Uses Graphiti's get_nodes_by_query() for official node discovery")
        print("   - Uses Graphiti's _search() with NODE_HYBRID_SEARCH_RRF for comprehensive coverage")
        print("   - Combines results to ensure no nodes are missed")
        print("   - Uses Neo4j batch deletion for efficiency")
        
        # Perform the deletion
        delete_result = manager._delete_memory(user_id)
        print(f"\n   Delete operation completed!")
        
        # Show detailed results
        if delete_result.get("status") == "success":
            method = delete_result.get("method", "unknown")
            nodes_found = delete_result.get("nodes_found", 0)
            deleted_count = delete_result.get("deleted_count", 0)
            
            print(f"   ✅ Method: {method}")
            print(f"   ✅ Nodes discovered: {nodes_found}")
            print(f"   ✅ Nodes deleted: {deleted_count}")
            
            if nodes_found == deleted_count:
                print("   ✅ Perfect match - all discovered nodes were successfully deleted")
            else:
                print(f"   ⚠️  Mismatch: found {nodes_found} but deleted {deleted_count}")
        else:
            print(f"   ❌ Deletion failed: {delete_result.get('error', 'Unknown error')}")
        
        print(f"\n4. Verifying deletion was complete...")
        
        # Search again to verify deletion
        alice_after, _ = manager._search_memory(user_id, "Alice")
        work_after, _ = manager._search_memory(user_id, "work TechCorp")
        hobby_after, _ = manager._search_memory(user_id, "hiking cat")
        
        total_after = len(alice_after) + len(work_after) + len(hobby_after)
        
        print(f"   Alice-related memories after deletion: {len(alice_after)}")
        print(f"   Work-related memories after deletion: {len(work_after)}")
        print(f"   Hobby-related memories after deletion: {len(hobby_after)}")
        print(f"   Total memories found after deletion: {total_after}")
        
        if total_after == 0:
            print("   ✅ Complete deletion verified - no memories remain")
        else:
            print(f"   ⚠️  {total_after} memories still found - deletion may be incomplete")
        
        print(f"\n5. Testing edge cases...")
        
        # Test deletion of non-existent user
        empty_result = manager._delete_memory("non_existent_user_12345")
        print(f"   Non-existent user deletion: {empty_result.get('deleted_count', 0)} nodes deleted")
        
        # Test deletion of already-deleted user
        repeat_result = manager._delete_memory(user_id)
        print(f"   Repeat deletion: {repeat_result.get('deleted_count', 0)} nodes deleted")
        
        print(f"\n✅ Demo completed successfully!")
        print(f"\n🎯 Key advantages of the new approach:")
        print(f"   • Uses official Graphiti APIs for node discovery")
        print(f"   • Combines multiple search methods for comprehensive coverage")
        print(f"   • Efficient batch deletion instead of individual node deletions")
        print(f"   • Provides detailed feedback about the deletion process")
        print(f"   • Maintains safety through parameterized queries")
        
    except Exception as e:
        print(f"❌ Demo failed with error: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # Clean up connections
        print(f"\n6. Cleaning up connections...")
        manager.close_all_connections()
        print("✅ Demo cleanup completed")


if __name__ == "__main__":
    demo_search_based_delete()
