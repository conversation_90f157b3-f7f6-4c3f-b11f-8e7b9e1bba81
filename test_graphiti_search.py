#!/usr/bin/env python3
"""
Simple test to verify Graphiti search functionality works
"""

from graphiti.manager import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>


def test_search_functionality():
    """Test basic search functionality without adding new episodes"""
    print("=== Testing Graphiti Search Functionality ===\n")
    
    # Initialize the manager
    manager = GraphitiManager(is_graph=True)
    
    try:
        print("1. Testing basic search...")
        
        # Test basic search functionality
        user_id = "test_user"
        query = "test query"
        
        semantic_memories, graph_memories = manager._search_memory(user_id, query)
        
        print(f"   Search completed successfully!")
        print(f"   Found {len(semantic_memories)} semantic memories")
        if graph_memories:
            print(f"   Found {len(graph_memories)} graph memories")
        else:
            print("   No graph memories returned (expected for empty database)")
        
        print(f"\n2. Testing delete functionality...")
        
        # Test delete functionality
        delete_result = manager._delete_memory(user_id)
        print(f"   Delete result: {delete_result}")
        
        print(f"\n✅ All tests completed successfully!")
        print(f"✅ The original 'get_nodes_by_query' error has been fixed!")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # Clean up connections
        print(f"\n3. Cleaning up connections...")
        manager.close_all_connections()
        print("✅ Test cleanup completed")


if __name__ == "__main__":
    test_search_functionality()
